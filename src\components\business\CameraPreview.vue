<template>
  <view class="camera-container">
    <view v-if="!cameraAuthorized" class="camera-unauthorized">
      <u-icon name="camera" size="60" color="#999" />
      <u-text 
        text="需要摄像头权限进行人脸识别" 
        size="md" 
        color="#666" 
        align="center" 
        class="auth-tip"
      />
      <u-button
        text="开启摄像头权限"
        type="primary"
        size="medium"
        :custom-style="authButtonStyle"
        @click="handleRequestAuth"
      />
    </view>
    
    <view v-else class="camera-preview">
      <camera 
        id="face-verification-camera"
        class="camera"
        device-position="front"
        flash="off"
        :auto-focus="true"
        mode="normal"
        resolution="medium"
        @initdone="onCameraReady"
        @error="onCameraError"
        @stop="onCameraStop"
      />
      
      <FaceGuide />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import FaceGuide from './FaceGuide.vue';

interface Props {
  cameraAuthorized: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'camera-ready'): void;
  (e: 'camera-error', error: any): void;
  (e: 'request-auth'): void;
}>();

/** 授权按钮样式 */
const authButtonStyle = computed(() => ({
  background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
  color: '#ffffff',
  border: 'none',
  borderRadius: '12rpx',
  boxShadow: '0 4rpx 16rpx rgba(102, 187, 106, 0.3)',
  fontSize: '28rpx',
  fontWeight: '500',
}));

function onCameraReady() {
  console.log('摄像头准备就绪');
  emit('camera-ready');
}

function onCameraError(error: any) {
  console.error('摄像头错误:', error);
  emit('camera-error', error);
}

function onCameraStop() {
  console.log('摄像头停止');
}

function handleRequestAuth() {
  emit('request-auth');
}
</script>

<style lang="scss" scoped>
.camera-container {
  flex: 1;
  margin: 24rpx 0;
  position: relative;
  z-index: 10;
  min-height: 600rpx;
  height: 60vh;
  display: flex;
  flex-direction: column;
  
  .camera-unauthorized {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24rpx;
    padding: 48rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }
  
  .camera-preview {
    height: 100%;
    border-radius: 24rpx;
    overflow: hidden;
    position: relative;
    background: #000;
    
    .camera {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: block;
      background: #000;
    }
  }
}

/* 响应式设计 */
@media screen and (max-height: 600px) {
  .camera-container {
    margin: 16rpx 0;
    height: 50vh;
    min-height: 400rpx;
  }
}
</style>
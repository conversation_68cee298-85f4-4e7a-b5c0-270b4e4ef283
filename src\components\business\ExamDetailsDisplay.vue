<template>
  <view class="exam-details-display">
    <!-- 考试基本信息 -->
    <view class="exam-header">
      <text class="exam-title">
        {{ examInfo.name }}
      </text>
      <view
        class="exam-status"
        :class="examInfo.status"
      >
        {{ getStatusText(examInfo.status) }}
      </view>
    </view>

    <!-- 考试详细信息 -->
    <view class="exam-details">
      <view class="detail-section">
        <text class="section-title">
          考试信息
        </text>
        
        <view class="detail-items">
          <view class="detail-item">
            <view class="item-icon">
              📅
            </view>
            <view class="item-content">
              <text class="item-label">
                考试时间
              </text>
              <text class="item-value">
                {{ formatDateTime(examInfo.examTime) }}
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              📍
            </view>
            <view class="item-content">
              <text class="item-label">
                考试地点
              </text>
              <text class="item-value">
                {{ examInfo.location }}
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              ⏱️
            </view>
            <view class="item-content">
              <text class="item-label">
                考试时长
              </text>
              <text class="item-value">
                {{ examInfo.duration }}分钟
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              📝
            </view>
            <view class="item-content">
              <text class="item-label">
                题目数量
              </text>
              <text class="item-value">
                {{ examInfo.totalQuestions }}题
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              🎯
            </view>
            <view class="item-content">
              <text class="item-label">
                及格分数
              </text>
              <text class="item-value">
                {{ examInfo.passScore }}分
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 报名信息 -->
      <view class="detail-section">
        <text class="section-title">
          报名信息
        </text>
        
        <view class="detail-items">
          <view class="detail-item">
            <view class="item-icon">
              📋
            </view>
            <view class="item-content">
              <text class="item-label">
                报名截止
              </text>
              <text class="item-value">
                {{ formatDateTime(examInfo.registrationDeadline) }}
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              👥
            </view>
            <view class="item-content">
              <text class="item-label">
                报名人数
              </text>
              <text class="item-value">
                {{ examInfo.registeredCount }}/{{ examInfo.maxCapacity }}
              </text>
            </view>
          </view>
          
          <view class="detail-item">
            <view class="item-icon">
              💰
            </view>
            <view class="item-content">
              <text class="item-label">
                考试费用
              </text>
              <text class="item-value">
                {{ examInfo.fee > 0 ? `¥${examInfo.fee}` : '免费' }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 考试说明 -->
      <view class="detail-section">
        <text class="section-title">
          考试说明
        </text>
        
        <view class="exam-description">
          <text class="description-text">
            {{ examInfo.description }}
          </text>
        </view>
        
        <view class="exam-requirements">
          <text class="requirements-title">
            考试要求：
          </text>
          <view class="requirements-list">
            <view
              v-for="requirement in examInfo.requirements"
              :key="requirement"
              class="requirement-item"
            >
              <text class="requirement-icon">
                •
              </text>
              <text class="requirement-text">
                {{ requirement }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 携带物品 -->
      <view class="detail-section">
        <text class="section-title">
          携带物品
        </text>
        
        <view class="items-grid">
          <view
            v-for="item in examInfo.requiredItems"
            :key="item"
            class="item-card"
          >
            <view class="item-icon">
              {{ getItemIcon(item) }}
            </view>
            <text class="item-name">
              {{ item }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  examInfo: any;
}

const props = defineProps<Props>();

function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    open: '报名中',
    closed: '报名结束',
    full: '报名已满',
    completed: '已结束',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
}

function getItemIcon(item: string) {
  const iconMap: Record<string, string> = {
    '身份证': '🆔',
    '准考证': '📄',
    '2B铅笔': '✏️',
    '橡皮': '🧽',
    '黑色签字笔': '🖊️',
    '计算器': '🧮',
    '尺子': '📏',
  };
  return iconMap[item] || '📦';
}

function formatDateTime(dateTimeStr: string) {
  const date = new Date(dateTimeStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.exam-details-display {
  .exam-header {
    background: linear-gradient(135deg, $secondary-color, $secondary-light);
    padding: $spacing-xl $spacing-lg;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .exam-title {
      flex: 1;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: white;
      margin-right: $spacing-md;
    }

    .exam-status {
      padding: $spacing-xs $spacing-sm;
      border-radius: $border-radius-small;
      font-size: $font-size-xs;
      color: white;

      &.open {
        background-color: $success-color;
      }

      &.closed, &.full {
        background-color: $error-color;
      }

      &.completed {
        background-color: $text-disabled;
      }
    }
  }

  .exam-details {
    padding: $spacing-lg;

    .detail-section {
      background-color: $surface-color;
      border-radius: $border-radius-large;
      padding: $spacing-xl;
      margin-bottom: $spacing-lg;
      box-shadow: $shadow-light;

      .section-title {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-lg;
        padding-bottom: $spacing-sm;
        border-bottom: 2rpx solid $divider-color;
      }

      .detail-items {
        .detail-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: $spacing-lg;

          &:last-child {
            margin-bottom: 0;
          }

          .item-icon {
            font-size: $font-size-lg;
            margin-right: $spacing-md;
            margin-top: 4rpx;
          }

          .item-content {
            flex: 1;

            .item-label {
              display: block;
              font-size: $font-size-sm;
              color: $text-secondary;
              margin-bottom: $spacing-xs;
            }

            .item-value {
              font-size: $font-size-md;
              color: $text-primary;
              font-weight: $font-weight-medium;
            }
          }
        }
      }

      .exam-description {
        margin-bottom: $spacing-lg;

        .description-text {
          font-size: $font-size-md;
          line-height: 1.6;
          color: $text-secondary;
        }
      }

      .exam-requirements {
        .requirements-title {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-md;
        }

        .requirements-list {
          .requirement-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: $spacing-sm;

            .requirement-icon {
              color: $secondary-color;
              margin-right: $spacing-sm;
              margin-top: 2rpx;
            }

            .requirement-text {
              flex: 1;
              font-size: $font-size-sm;
              color: $text-secondary;
              line-height: 1.5;
            }
          }
        }
      }

      .items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
        gap: $spacing-md;

        .item-card {
          background-color: $background-color;
          border-radius: $border-radius-medium;
          padding: $spacing-lg;
          text-align: center;

          .item-icon {
            display: block;
            font-size: 60rpx;
            margin-bottom: $spacing-sm;
          }

          .item-name {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }
      }
    }
  }
}
</style>
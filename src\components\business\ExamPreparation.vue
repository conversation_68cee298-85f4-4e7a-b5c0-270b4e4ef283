<template>
  <view class="preparation-stage">
    <!-- 背景装饰层 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle--1" />
      <view class="decoration-circle decoration-circle--2" />
      <view class="decoration-circle decoration-circle--3" />
    </view>

    <!-- 顶部：考试信息 -->
    <view class="exam-info-section">
      <view class="exam-info">
        <u-text 
          :text="examInfo.name" 
          type="primary" 
          size="lg" 
          bold 
          align="center" 
          class="exam-title"
        />
        <view class="exam-details">
          <view class="detail-item">
            <u-icon name="clock-fill" size="18" color="#4CAF50" />
            <u-text :text="`考试时长：${examInfo.duration}分钟`" size="md" color="#666" />
          </view>
          <view class="detail-item">
            <u-icon name="file-text-fill" size="18" color="#2196F3" />
            <u-text :text="`题目数量：${examInfo.totalQuestions}题`" size="md" color="#666" />
          </view>
          <view class="detail-item">
            <u-icon name="checkmark-circle-fill" size="18" color="#FF9800" />
            <u-text :text="`及格分数：${examInfo.passScore}分`" size="md" color="#666" />
          </view>
        </view>
      </view>
    </view>

    <!-- 中间：考前须知内容 -->
    <view class="exam-rules-section">
      <scroll-view 
        class="rules-scroll-view"
        scroll-y
        :scroll-top="scrollTop"
        @scroll="handleScroll"
        @scrolltolower="handleScrollToLower"
      >
        <view 
          v-if="examRules" 
          class="rules-content" 
          v-html="examRules"
        />
        <view 
          v-else 
          class="rules-loading"
        >
          <u-loading-icon mode="spinner" color="#4CAF50" />
          <u-text text="加载考前须知中..." size="sm" color="#999" />
        </view>
      </scroll-view>
    </view>

    <!-- 底部：确认按钮区域 -->
    <view class="action-section">
      
      <!-- 确认按钮 -->
      <u-button
        v-if="typeof buttonText === 'string'"
        :text="buttonText"
        type="primary"
        :disabled="!canStartExam"
        :loading="false"
        :custom-style="actionButtonStyle"
        shape="circle"
        size="large"
        @click="handleStartExam"
      />
      
      <!-- 倒计时按钮 -->
      <view 
        v-else
        class="countdown-button"
        :style="actionButtonStyle"
      >
        <view class="countdown-text">{{ buttonText.text }}</view>
        <view class="countdown-number">{{ buttonText.countdown }}s</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';

interface Props {
  examInfo: any;
  examRules: string;
  countdownTime: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'start-exam'): void;
}>();

const scrollTop = ref(0);
const hasScrollToBottom = ref(false);
const hasScrollBar = ref(false);

const buttonText = computed(() => {
  if (!props.examRules) {
    return '加载中...';
  }
  if (props.countdownTime > 0) {
    return { 
      text: '请完整阅读"考前须知"后再进入下一步', 
      countdown: props.countdownTime 
    };
  }
  if (hasScrollBar.value && !hasScrollToBottom.value) {
    return '请滚动到底部阅读完整内容';
  }
  return '下一步';
});

/** 按钮样式 - 医疗健康主题 */
const actionButtonStyle = computed(() => ({
  width: '100%',
  height: '88rpx',
  background: canStartExam.value
    ? '#66BB6A'
    : '#e0e0e0',
  color: '#0a0a0a',
  border: 'none',
  borderRadius: '44rpx',
  boxShadow: canStartExam.value
    ? '0 8rpx 24rpx rgba(102, 187, 106, 0.3)'
    : '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  opacity: canStartExam.value ? 1 : 0.6,
  transform: canStartExam.value ? 'translateY(0)' : 'translateY(2rpx)',
}));

const canStartExam = computed(() => {
  if (!props.examRules || props.countdownTime > 0) {
    return false;
  }
  
  // 如果有滚动条，需要滚动到底部
  if (hasScrollBar.value && !hasScrollToBottom.value) {
    return false;
  }
  
  return true;
});

onMounted(() => {
  nextTick(() => {
    checkScrollBar();
  });
});

function checkScrollBar() {
  // 检查是否有滚动条
  const query = uni.createSelectorQuery();
  query.select('.rules-scroll-view').boundingClientRect((scrollViewRect) => {
    query.select('.rules-content').boundingClientRect((contentRect) => {
      if (scrollViewRect && contentRect) {
        hasScrollBar.value = contentRect.height > scrollViewRect.height;
      }
    });
  }).exec();
}

function handleScroll(e: any) {
  scrollTop.value = e.detail.scrollTop;
}

function handleScrollToLower() {
  hasScrollToBottom.value = true;
}

function handleStartExam() {
  if (!canStartExam.value) return;
  emit('start-exam');
}

// 监听examRules变化，重新检查滚动条
watch(() => props.examRules, () => {
  if (props.examRules) {
    // 重置滚动状态
    hasScrollToBottom.value = false;
    nextTick(() => {
      checkScrollBar();
    });
  }
});

// 监听倒计时变化，当倒计时结束后检查滚动条
watch(() => props.countdownTime, (newTime) => {
  if (newTime === 0) {
    nextTick(() => {
      checkScrollBar();
    });
  }
});
</script>

<style lang="scss" scoped>
/*
  疾控考试系统考前准备页面样式
  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变
  与login.vue保持一致的背景设计
*/

/* ==================== 页面基础设置 ==================== */
.preparation-stage {
  height: 100vh;
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ==================== 背景装饰 ==================== */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));

  &--1 {
    width: 320rpx;
    height: 320rpx;
    top: -160rpx;
    right: -160rpx;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(33, 150, 243, 0.08));
  }

  &--2 {
    width: 240rpx;
    height: 240rpx;
    bottom: 20%;
    left: -120rpx;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.06), rgba(76, 175, 80, 0.06));
  }

  &--3 {
    width: 180rpx;
    height: 180rpx;
    top: 30%;
    left: 20%;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.04), rgba(33, 150, 243, 0.04));
  }
}

/* ==================== 顶部考试信息区域 ==================== */
.exam-info-section {
  flex-shrink: 0;
  padding: 40rpx 32rpx 24rpx;
  position: relative;
  z-index: 10;

  .exam-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    .exam-title {
      margin-bottom: 24rpx;
      display: block;
      width: 100%;
    }

    .exam-details {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        padding: 8rpx 0;
      }
    }
  }
}

/* ==================== 中间考前须知区域（可滚动） ==================== */
.exam-rules-section {
  flex: 1;
  padding: 16rpx 32rpx;
  position: relative;
  z-index: 10;
  min-height: 0;

  .rules-scroll-view {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    .rules-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      word-wrap: break-word;
      
      :deep(ol), :deep(ul) {
        padding-left: 32rpx;
        margin-bottom: 16rpx;
      }
      
      :deep(li) {
        margin-bottom: 8rpx;
      }
      
      :deep(h1), :deep(h2), :deep(h3) {
        color: #2c5aa0;
        margin: 16rpx 0 12rpx 0;
        font-weight: 600;
      }

      :deep(p) {
        margin-bottom: 12rpx;
      }

      :deep(strong) {
        color: #2c5aa0;
        font-weight: 600;
      }
    }

    .rules-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      padding: 80rpx 32rpx;
      min-height: 200rpx;
    }
  }
}

/* ==================== 底部按钮区域 ==================== */
.action-section {
  flex-shrink: 0;
  padding: 16rpx 32rpx 40rpx;
  position: relative;
  z-index: 10;
  background: transparent;

  .action-tip {
    background: rgba(255, 107, 53, 0.1);
    border-radius: 16rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 20rpx;
    border: 1rpx solid rgba(255, 107, 53, 0.2);
    backdrop-filter: blur(10rpx);
  }
}

/* 倒计时按钮样式 */
.countdown-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  cursor: default;
  padding: 0 24rpx;
  box-sizing: border-box;
  
  .countdown-text {
    font-size: 30rpx;
    color: #333333;
    font-weight: 600;
    line-height: 1.3;
    text-align: left;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 120rpx);
  }
  
  .countdown-number {
    font-size: 36rpx;
    font-weight: 700;
    color: #333333;
    line-height: 1;
    padding: 8rpx 16rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    min-width: 80rpx;
    max-width: 100rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
  }
}

/* 响应式字体调整 */
@media screen and (max-width: 750rpx) {
  .countdown-button {
    padding: 0 20rpx;
    
    .countdown-text {
      font-size: 28rpx;
      max-width: calc(100% - 100rpx);
    }
    
    .countdown-number {
      font-size: 32rpx;
      min-width: 70rpx;
      max-width: 90rpx;
      padding: 6rpx 12rpx;
    }
  }
}

@media screen and (max-width: 600rpx) {
  .countdown-button {
    gap: 8rpx;
    padding: 0 16rpx;
    
    .countdown-text {
      font-size: 26rpx;
      max-width: calc(100% - 80rpx);
    }
    
    .countdown-number {
      font-size: 30rpx;
      min-width: 60rpx;
      max-width: 80rpx;
      padding: 4rpx 10rpx;
    }
  }
}

/* ==================== uview-plus组件样式定制 ==================== */
/* 按钮激活效果 */
:deep(.u-button) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  
  &:not(.u-button--disabled):active {
    transform: translateY(2rpx) !important;
    opacity: 0.9 !important;
  }
}

/* 文本组件样式优化 */
:deep(.u-text) {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* 图标颜色渐变效果 */
:deep(.u-icon) {
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1)) !important;
}

/* ==================== 响应式设计 ==================== */
@media screen and (max-height: 600px) {
  .preparation-stage {
    .exam-info-section {
      padding: 24rpx 32rpx 16rpx;
      
      .exam-info {
        padding: 24rpx 20rpx;
      }
    }
    
    .exam-rules-section {
      padding: 12rpx 32rpx;
      
      .rules-scroll-view {
        padding: 20rpx;
      }
    }
    
    .action-section {
      padding: 12rpx 32rpx 24rpx;
      
      .action-tip {
        padding: 12rpx 16rpx;
        margin-bottom: 16rpx;
      }
    }
  }
}

@media screen and (max-height: 500px) {
  .preparation-stage {
    .exam-info-section {
      padding: 16rpx 32rpx 12rpx;
      
      .exam-info {
        padding: 20rpx 16rpx;
        
        .exam-details {
          gap: 12rpx;
          
          .detail-item {
            padding: 4rpx 0;
          }
        }
      }
    }
    
    .exam-rules-section {
      padding: 8rpx 32rpx;
      
      .rules-scroll-view {
        padding: 16rpx;
      }
    }
    
    .action-section {
      padding: 8rpx 32rpx 20rpx;
    }
  }
}
</style>
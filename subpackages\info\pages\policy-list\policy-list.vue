<template>
  <view class="policy-list-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">
        政策法规
      </text>
      <text class="page-desc">
        查看相关政策法规文件
      </text>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-section">
      <scroll-view
        class="filter-scroll"
        scroll-x
      >
        <view class="filter-tabs">
          <view 
            v-for="category in categories" 
            :key="category.id"
            class="filter-tab"
            :class="{ active: selectedCategory === category.id }"
            @tap="selectCategory(category.id)"
          >
            {{ category.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 政策列表 -->
    <view class="policy-list">
      <view
        v-if="loading"
        class="loading-state"
      >
        <u-loading-icon mode="spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>

      <view
        v-else-if="policyList.length === 0"
        class="empty-state"
      >
        <text class="empty-icon">
          📋
        </text>
        <text class="empty-text">
          暂无政策法规
        </text>
      </view>

      <view v-else>
        <view 
          v-for="item in policyList" 
          :key="item.id"
          class="policy-item"
          @tap="goToDetail(item)"
        >
          <view class="item-header">
            <text class="item-title">
              {{ item.title }}
            </text>
            <view class="item-category">
              {{ getCategoryName(item.categoryId) }}
            </view>
          </view>
          
          <text class="item-summary">
            {{ item.summary }}
          </text>
          
          <view class="item-footer">
            <text class="item-time">
              发布时间：{{ formatTime(item.publishTime) }}
            </text>
            <text
              class="item-status"
              :class="item.status"
            >
              {{ getStatusText(item.status) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view
      v-if="hasMore && !loading"
      class="load-more"
      @tap="loadMore"
    >
      <text class="load-more-text">
        点击加载更多
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getPolicyList } from '../../../../src/api/modules/info';
import type { InfoItem } from '../../../../src/types/api';

// 响应式数据
const policyList = ref<InfoItem[]>([]);
const loading = ref(false);
const selectedCategory = ref('all');
const currentPage = ref(1);
const hasMore = ref(true);
const pageSize = 10;

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'law', name: '法律法规' },
  { id: 'policy', name: '政策文件' },
  { id: 'standard', name: '行业标准' },
  { id: 'guideline', name: '指导意见' },
]);

onMounted(() => {
  loadPolicyList();
});

/**
 * 加载政策列表
 */
async function loadPolicyList(isLoadMore = false) {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const params = {
      page: isLoadMore ? currentPage.value : 1,
      pageSize,
      category: selectedCategory.value === 'all' ? undefined : selectedCategory.value,
    };
    
    const result = await getPolicyList(params);
    
    if (isLoadMore) {
      policyList.value.push(...result);
    } else {
      policyList.value = result;
      currentPage.value = 1;
    }
    
    hasMore.value = result.length === pageSize;
    
  } catch (error) {
    console.error('加载政策列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 选择分类
 */
function selectCategory(categoryId: string) {
  selectedCategory.value = categoryId;
  currentPage.value = 1;
  loadPolicyList();
}

/**
 * 加载更多
 */
function loadMore() {
  currentPage.value++;
  loadPolicyList(true);
}

/**
 * 跳转到详情页
 */
function goToDetail(item: InfoItem) {
  uni.navigateTo({
    url: `/subpackages/info/pages/detail/detail?id=${item.id}&type=policy`,
  });
}

/**
 * 获取分类名称
 */
function getCategoryName(categoryId: string) {
  const category = categories.value.find(c => c.id === categoryId);
  return category?.name || '其他';
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    active: '有效',
    expired: '已失效',
    draft: '草案',
  };
  return statusMap[status] || status;
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleDateString();
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.policy-list-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $secondary-color, $secondary-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.filter-section {
  background-color: white;
  border-bottom: 1rpx solid $divider-color;

  .filter-scroll {
    white-space: nowrap;

    .filter-tabs {
      display: flex;
      padding: $spacing-sm $spacing-md;

      .filter-tab {
        flex-shrink: 0;
        padding: $spacing-sm $spacing-md;
        margin-right: $spacing-sm;
        border-radius: $border-radius-medium;
        font-size: $font-size-sm;
        color: $text-secondary;
        background-color: $background-color;

        &.active {
          background-color: $secondary-color;
          color: white;
        }
      }
    }
  }
}

.policy-list {
  padding: $spacing-md;

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;

    .loading-text, .empty-text {
      margin-top: $spacing-md;
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .empty-icon {
      font-size: 120rpx;
      opacity: 0.5;
    }
  }

  .policy-item {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-light;

    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: $spacing-sm;

      .item-title {
        flex: 1;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        line-height: 1.4;
        margin-right: $spacing-sm;
      }

      .item-category {
        background-color: $secondary-light;
        color: $secondary-color;
        font-size: $font-size-xs;
        padding: 4rpx 8rpx;
        border-radius: $border-radius-small;
        white-space: nowrap;
      }
    }

    .item-summary {
      display: block;
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.5;
      margin-bottom: $spacing-md;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .item-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-time {
        font-size: $font-size-xs;
        color: $text-disabled;
      }

      .item-status {
        font-size: $font-size-xs;

        &.active {
          color: $success-color;
        }

        &.expired {
          color: $error-color;
        }

        &.draft {
          color: $warning-color;
        }
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: $spacing-lg;

  .load-more-text {
    font-size: $font-size-sm;
    color: $secondary-color;
  }
}
</style>

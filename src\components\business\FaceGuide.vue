<template>
  <view class="face-guide">
    <!-- 四边遮罩层 -->
    <view class="mask-top" />
    <view class="mask-bottom" />
    <view class="mask-left" />
    <view class="mask-right" />
    
    <!-- 椭圆形人脸框 -->
    <view class="face-outline">
      <view class="face-border">
        <!-- 四角标记 -->
        <view class="corner corner-tl" />
        <view class="corner corner-tr" />
        <view class="corner corner-bl" />
        <view class="corner corner-br" />
        
        <!-- 扫描线动画 -->
        <view class="scan-line" />
      </view>
    </view>
    
    <!-- 引导文字 -->
    <view class="guide-text-wrapper">
      <u-text 
        text="请将面部置于框内" 
        size="md" 
        color="#ffffff" 
        align="center" 
        class="guide-text"
      />
      <u-text 
        text="保持正脸，光线充足" 
        size="sm" 
        color="#ffffff" 
        align="center" 
        class="guide-sub-text"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
// 人脸指导框组件，纯展示组件无需props
</script>

<style lang="scss" scoped>
.face-guide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;  // 降低z-index，仅比camera稍高即可
  pointer-events: none;
  
  // 定义人脸框尺寸和位置
  $face-width: 280rpx;
  $face-height: 360rpx;
  $face-top: 50%;
  $face-left: 50%;
  
  // 四边遮罩层
  .mask-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: calc(#{$face-top} - #{$face-height} / 2);
    background: rgba(0, 0, 0, 0.6);
  }
  
  .mask-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(50% - #{$face-height} / 2);
    background: rgba(0, 0, 0, 0.6);
  }
  
  .mask-left {
    position: absolute;
    top: calc(#{$face-top} - #{$face-height} / 2);
    left: 0;
    width: calc(#{$face-left} - #{$face-width} / 2);
    height: $face-height;
    background: rgba(0, 0, 0, 0.6);
  }
  
  .mask-right {
    position: absolute;
    top: calc(#{$face-top} - #{$face-height} / 2);
    right: 0;
    width: calc(50% - #{$face-width} / 2);
    height: $face-height;
    background: rgba(0, 0, 0, 0.6);
  }
  
  // 人脸轮廓框
  .face-outline {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: $face-width;
    height: $face-height;
    
    .face-border {
      width: 100%;
      height: 100%;
      border: 3rpx solid rgba(102, 187, 106, 0.8);
      border-radius: 50%;
      box-shadow: 
        0 0 20rpx rgba(102, 187, 106, 0.6),
        inset 0 0 20rpx rgba(102, 187, 106, 0.3);
      position: relative;
      animation: border-pulse 2s ease-in-out infinite;
      
      // 四角标记
      .corner {
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        border: 3rpx solid #66BB6A;
        
        &.corner-tl {
          top: -3rpx;
          left: -3rpx;
          border-right: none;
          border-bottom: none;
          border-top-left-radius: 12rpx;
        }
        
        &.corner-tr {
          top: -3rpx;
          right: -3rpx;
          border-left: none;
          border-bottom: none;
          border-top-right-radius: 12rpx;
        }
        
        &.corner-bl {
          bottom: -3rpx;
          left: -3rpx;
          border-right: none;
          border-top: none;
          border-bottom-left-radius: 12rpx;
        }
        
        &.corner-br {
          bottom: -3rpx;
          right: -3rpx;
          border-left: none;
          border-top: none;
          border-bottom-right-radius: 12rpx;
        }
      }
      
      // 扫描线动画
      .scan-line {
        position: absolute;
        width: calc(100% - 40rpx);
        height: 2rpx;
        left: 20rpx;
        background: linear-gradient(90deg, 
          transparent 0%, 
          #66BB6A 20%,
          #66BB6A 80%,
          transparent 100%);
        box-shadow: 0 0 10rpx #66BB6A;
        animation: scan 3s linear infinite;
        opacity: 0.8;
      }
    }
  }
  
  // 引导文字区域
  .guide-text-wrapper {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    
    .guide-text {
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 8rpx;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
    }
    
    .guide-sub-text {
      display: block;
      font-size: 26rpx;
      opacity: 0.9;
      text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.8);
    }
  }
}

// 边框脉冲动画
@keyframes border-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

// 扫描线动画
@keyframes scan {
  0% {
    top: 10%;
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    top: 90%;
    opacity: 0;
  }
}

// 响应式设计
@media screen and (max-height: 600px) {
  .face-guide {
    $face-width: 240rpx;
    $face-height: 320rpx;
    
    .mask-left {
      width: calc(50% - 120rpx);
    }
    
    .mask-right {
      width: calc(50% - 120rpx);
    }
    
    .face-outline {
      width: 240rpx;
      height: 320rpx;
    }
    
    .guide-text-wrapper {
      bottom: 15%;
      
      .guide-text {
        font-size: 28rpx;
      }
      
      .guide-sub-text {
        font-size: 24rpx;
      }
    }
  }
}
</style>
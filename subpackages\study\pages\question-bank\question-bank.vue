<template>
  <view class="question-bank-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">
        题库分类
      </text>
      <text class="page-desc">
        选择题库开始练习
      </text>
    </view>

    <!-- 今日练习统计 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">
          今日练习统计
        </text>
        <text class="stats-date">
          {{ getCurrentDate() }}
        </text>
      </view>
      
      <view class="stats-content">
        <view class="stats-item">
          <text class="stats-number">
            {{ todayPracticeCount }}
          </text>
          <text class="stats-label">
            已练习
          </text>
        </view>
        <view class="stats-item">
          <text class="stats-number">
            {{ remainingCount }}
          </text>
          <text class="stats-label">
            剩余次数
          </text>
        </view>
        <view class="stats-item">
          <text class="stats-number">
            {{ todayCorrectRate }}%
          </text>
          <text class="stats-label">
            正确率
          </text>
        </view>
      </view>
    </view>

    <!-- 题库分类列表 -->
    <view class="category-list">
      <view
        v-if="loading"
        class="loading-state"
      >
        <u-loading-icon mode="spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>

      <view
        v-else-if="categoryList.length === 0"
        class="empty-state"
      >
        <text class="empty-icon">
          📚
        </text>
        <text class="empty-text">
          暂无题库分类
        </text>
      </view>

      <view v-else>
        <view 
          v-for="category in categoryList" 
          :key="category.id"
          class="category-item"
          :class="{ disabled: !canPractice(category) }"
          @tap="selectCategory(category)"
        >
          <view class="category-icon">
            {{ category.icon || '📝' }}
          </view>
          
          <view class="category-info">
            <text class="category-name">
              {{ category.name }}
            </text>
            <text class="category-desc">
              {{ category.description }}
            </text>
            
            <view class="category-stats">
              <text class="stats-text">
                共{{ category.totalQuestions }}题
              </text>
              <text class="stats-text">
                已练{{ category.practiceCount || 0 }}次
              </text>
            </view>
          </view>
          
          <view class="category-action">
            <view
              v-if="category.isLocked"
              class="lock-badge"
            >
              🔒 未解锁
            </view>
            <view
              v-else-if="!canPractice(category)"
              class="limit-badge"
            >
              今日已达上限
            </view>
            <view
              v-else
              class="practice-btn"
            >
              开始练习
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 练习说明 -->
    <view class="practice-rules">
      <text class="rules-title">
        练习说明
      </text>
      <view class="rules-content">
        <view class="rule-item">
          <text class="rule-icon">
            •
          </text>
          <text class="rule-text">
            每日最多可练习3组题目
          </text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">
            •
          </text>
          <text class="rule-text">
            每组包含20道题目，限时30分钟
          </text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">
            •
          </text>
          <text class="rule-text">
            练习完成后可查看答案解析
          </text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">
            •
          </text>
          <text class="rule-text">
            建议按分类逐步练习，巩固知识点
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getQuestionCategories, getPracticeStats } from '../../../../src/api/modules/study';

// 响应式数据
const categoryList = ref<any[]>([]);
const loading = ref(false);
const todayPracticeCount = ref(0);
const remainingCount = ref(3);
const todayCorrectRate = ref(0);

onMounted(() => {
  loadData();
});

/**
 * 加载数据
 */
async function loadData() {
  await Promise.all([
    loadCategories(),
    loadPracticeStats(),
  ]);
}

/**
 * 加载题库分类
 */
async function loadCategories() {
  try {
    loading.value = true;
    categoryList.value = await getQuestionCategories();
  } catch (error) {
    console.error('加载题库分类失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 加载练习统计
 */
async function loadPracticeStats() {
  try {
    const stats = await getPracticeStats();
    todayPracticeCount.value = stats.todayPracticeCount || 0;
    remainingCount.value = Math.max(0, 3 - todayPracticeCount.value);
    todayCorrectRate.value = stats.todayCorrectRate || 0;
  } catch (error) {
    console.error('加载练习统计失败:', error);
  }
}

/**
 * 判断是否可以练习
 */
function canPractice(category: any) {
  return !category.isLocked && remainingCount.value > 0;
}

/**
 * 选择分类开始练习
 */
function selectCategory(category: any) {
  if (category.isLocked) {
    uni.showToast({
      title: '该分类暂未解锁',
      icon: 'none',
    });
    return;
  }
  
  if (remainingCount.value <= 0) {
    uni.showModal({
      title: '练习次数不足',
      content: '今日练习次数已用完，明天再来吧！',
      showCancel: false,
    });
    return;
  }
  
  // 跳转到练习页面
  uni.navigateTo({
    url: `/subpackages/study/pages/practice/practice?categoryId=${category.id}&categoryName=${category.name}`,
  });
}

/**
 * 获取当前日期
 */
function getCurrentDate() {
  const now = new Date();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  return `${month}月${day}日`;
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.question-bank-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $success-color, $success-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.stats-card {
  margin: $spacing-lg;
  background-color: $surface-color;
  border-radius: $border-radius-large;
  padding: $spacing-lg;
  box-shadow: $shadow-medium;

  .stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-md;

    .stats-title {
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      color: $text-primary;
    }

    .stats-date {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }

  .stats-content {
    display: flex;
    justify-content: space-around;

    .stats-item {
      text-align: center;

      .stats-number {
        display: block;
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $success-color;
        margin-bottom: $spacing-xs;
      }

      .stats-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.category-list {
  padding: 0 $spacing-lg $spacing-lg;

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;

    .loading-text, .empty-text {
      margin-top: $spacing-md;
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .empty-icon {
      font-size: 120rpx;
      opacity: 0.5;
    }
  }

  .category-item {
    display: flex;
    align-items: center;
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-light;

    &.disabled {
      opacity: 0.6;

      .category-action .practice-btn {
        background-color: $text-disabled;
      }
    }

    .category-icon {
      font-size: 80rpx;
      margin-right: $spacing-lg;
    }

    .category-info {
      flex: 1;

      .category-name {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .category-desc {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.4;
        margin-bottom: $spacing-sm;
      }

      .category-stats {
        display: flex;
        gap: $spacing-md;

        .stats-text {
          font-size: $font-size-xs;
          color: $text-disabled;
        }
      }
    }

    .category-action {
      .lock-badge, .limit-badge {
        font-size: $font-size-xs;
        color: $text-disabled;
        text-align: center;
      }

      .practice-btn {
        background-color: $success-color;
        color: white;
        font-size: $font-size-sm;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-medium;
        text-align: center;
        min-width: 120rpx;
      }
    }
  }
}

.practice-rules {
  margin: $spacing-lg;
  background-color: $surface-color;
  border-radius: $border-radius-large;
  padding: $spacing-lg;

  .rules-title {
    display: block;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-md;
  }

  .rules-content {
    .rule-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: $spacing-sm;

      .rule-icon {
        color: $success-color;
        margin-right: $spacing-sm;
        margin-top: 2rpx;
      }

      .rule-text {
        flex: 1;
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.5;
      }
    }
  }
}
</style>

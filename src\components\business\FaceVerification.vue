<template>
  <view class="face-verification-stage">
    <!-- 背景装饰层 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle--1" />
      <view class="decoration-circle decoration-circle--2" />
      <view class="decoration-circle decoration-circle--3" />
    </view>

    <!-- 验证标题区域 -->
    <view class="verification-header">
      <u-text 
        text="人脸识别验证" 
        type="primary" 
        size="xl" 
        bold 
        align="center" 
        class="verification-title"
      />
      <u-text 
        text="请将面部对准摄像头，保持光线充足且面部清晰" 
        size="md" 
        color="#666" 
        align="center" 
        class="verification-desc"
      />
    </view>

    <!-- 摄像头预览区域 -->
    <CameraPreview 
      :camera-authorized="cameraAuthorized"
      @camera-ready="handleCameraReady"
      @camera-error="handleCameraError"
      @request-auth="requestCameraAuth"
    />

    <!-- 拍照按钮区域 -->
    <CaptureButton 
      :face-verifying="faceVerifying"
      :camera-authorized="cameraAuthorized"
      @capture-face="handleCaptureFace"
    />

    <!-- 人脸识别失败弹窗 -->
    <u-modal
      v-model:show="showFaceFailModal"
      title="身份验证失败"
      content="人脸识别验证失败，请重新验证或联系监考老师"
      :show-cancel-button="true"
      confirm-text="重新验证"
      cancel-text="退出考试"
      confirm-color="#66BB6A"
      @confirm="handleRetryVerification"
      @cancel="handleExitExam"
    />
    
    <!-- 权限引导弹窗 -->
    <u-modal
      v-model:show="showAuthModal"
      title="需要摄像头权限"
      content="为了进行人脸识别验证，需要您授权摄像头权限。请在设置中开启相应权限。"
      :show-cancel-button="true"
      confirm-text="去设置"
      cancel-text="取消"
      confirm-color="#66BB6A"
      @confirm="openSetting"
      @cancel="handleAuthCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useCameraAuth } from '@/src/composables/useCameraAuth';
import CameraPreview from './CameraPreview.vue';
import CaptureButton from './CaptureButton.vue';

const emit = defineEmits<{
  (e: 'camera-ready'): void;
  (e: 'camera-error', error: any): void;
  (e: 'face-verified'): void;
  (e: 'exit-exam'): void;
}>();

// 使用摄像头权限管理 composable
const {
  cameraAuthorized,
  showAuthModal,
  requestCameraAuth,
  openSetting,
  handleAuthCancel,
  onCameraReady,
  onCameraError,
} = useCameraAuth();

// 验证状态管理
const faceVerifying = ref(false);
const showFaceFailModal = ref(false);

/** 摄像头就绪处理 */
function handleCameraReady() {
  onCameraReady();
  emit('camera-ready');
}

/** 摄像头错误处理 */
function handleCameraError(error: any) {
  onCameraError(error);
  emit('camera-error', error);
}

/** 拍照验证处理 */
function handleCaptureFace() {
  // 添加点击反馈
  uni.vibrateShort({
    type: 'light'
  });
  
  faceVerifying.value = true;

  // 创建 camera 上下文对象以进行拍照
  const cameraContext = uni.createCameraContext();
  
  // 执行拍照
  cameraContext.takePhoto({
    quality: 'high',
    success: (res) => {
      console.log('拍照成功:', res.tempImagePath);
      
      // 模拟人脸识别验证过程
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90%成功率

        if (success) {
          // 验证成功，开始考试
          uni.showToast({
            title: '验证成功',
            icon: 'success',
            duration: 1500
          });
          
          setTimeout(() => {
            emit('face-verified');
          }, 1500);
        } else {
          // 验证失败
          showFaceFailModal.value = true;
        }

        faceVerifying.value = false;
      }, 2000);
    },
    fail: (err) => {
      console.error('拍照失败:', err);
      faceVerifying.value = false;
      
      uni.showToast({
        title: '拍照失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  });
}

/** 重试验证 */
function handleRetryVerification() {
  showFaceFailModal.value = false;
  // 重置验证状态
}

/** 退出考试 */
function handleExitExam() {
  emit('exit-exam');
}
</script>

<style lang="scss" scoped>
/*
  疾控考试系统人脸识别验证页面样式
  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变
  与login.vue保持一致的背景设计
*/

/* ==================== 页面基础设置 ==================== */
.face-verification-stage {
  height: 100vh;
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 30rpx 24rpx 0rpx;
  box-sizing: border-box;
}

/* ==================== 背景装饰 ==================== */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));

  &--1 {
    width: 320rpx;
    height: 320rpx;
    top: -160rpx;
    right: -160rpx;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(33, 150, 243, 0.08));
  }

  &--2 {
    width: 240rpx;
    height: 240rpx;
    bottom: 20%;
    left: -120rpx;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.06), rgba(76, 175, 80, 0.06));
  }

  &--3 {
    width: 180rpx;
    height: 180rpx;
    top: 30%;
    left: 20%;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.04), rgba(33, 150, 243, 0.04));
  }
}

/* ==================== 验证标题区域 ==================== */
.verification-header {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 48rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  .verification-title {
    display: block;
    margin-bottom: 16rpx;
  }

  .verification-desc {
    display: block;
    line-height: 1.5;
  }
}

/* ==================== uview-plus组件样式定制 ==================== */
:deep(.u-button) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  
  &:not(.u-button--disabled):active {
    transform: scale(0.95) !important;
    opacity: 0.9 !important;
  }
}

:deep(.u-text) {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

:deep(.u-loading-icon) {
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1)) !important;
}

/* ==================== 响应式设计 ==================== */
@media screen and (max-height: 600px) {
  .face-verification-stage {
    padding: 24rpx 28rpx;
  }
  
  .verification-header {
    margin-bottom: 32rpx;
    padding: 24rpx 20rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .face-verification-stage {
    padding: 20rpx 24rpx;
  }
}
</style>
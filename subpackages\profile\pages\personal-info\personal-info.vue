<template>
  <view class="personal-info-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">
        个人信息
      </text>
      <text class="page-desc">
        查看和管理个人资料
      </text>
    </view>

    <!-- 加载状态 -->
    <view
      v-if="loading"
      class="loading-state"
    >
      <u-loading-icon mode="spinner" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <!-- 个人信息内容 -->
    <view
      v-else
      class="info-content"
    >
      <!-- 头像和基本信息 -->
      <view class="profile-section">
        <view class="avatar-section">
          <image 
            class="avatar-image"
            :src="userInfo.avatar || '/static/default-avatar.png'"
            mode="aspectFill"
            @tap="previewAvatar"
          />
          
          <view class="avatar-info">
            <text class="user-nickname">
              {{ userInfo.nickname || '未设置昵称' }}
            </text>
            <view
              class="user-status"
              :class="userInfo.status"
            >
              {{ getStatusText(userInfo.status) }}
            </view>
          </view>
        </view>
        
        <!-- 认证状态提示 -->
        <view
          v-if="userInfo.status === 'rejected'"
          class="rejection-notice"
        >
          <view class="notice-header">
            <u-icon
              name="warning-fill"
              size="16"
              color="#f44336"
            />
            <text class="notice-title">
              审核未通过
            </text>
          </view>
          <text class="notice-content">
            {{ userInfo.rejectionReason || '请联系管理员了解详情' }}
          </text>
        </view>
      </view>

      <!-- 个人详细信息 -->
      <view class="details-section">
        <text class="section-title">
          个人详细信息
        </text>
        
        <view class="info-items">
          <view class="info-item">
            <text class="item-label">
              真实姓名
            </text>
            <text class="item-value">
              {{ userInfo.realName || '未填写' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              联系电话
            </text>
            <text class="item-value">
              {{ formatPhone(userInfo.phone) || '未填写' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              身份证号
            </text>
            <text class="item-value">
              {{ formatIdCard(userInfo.idCard) || '未填写' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              隶属机构
            </text>
            <text class="item-value">
              {{ userInfo.institutionName || '未选择' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              职位
            </text>
            <text class="item-value">
              {{ userInfo.positionName || '未选择' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              注册时间
            </text>
            <text class="item-value">
              {{ formatDateTime(userInfo.createdAt) }}
            </text>
          </view>
          
          <view
            v-if="userInfo.approvedAt"
            class="info-item"
          >
            <text class="item-label">
              审核通过时间
            </text>
            <text class="item-value">
              {{ formatDateTime(userInfo.approvedAt) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 账户信息 -->
      <view class="account-section">
        <text class="section-title">
          账户信息
        </text>
        
        <view class="info-items">
          <view class="info-item">
            <text class="item-label">
              用户ID
            </text>
            <text class="item-value">
              {{ userInfo.id }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              微信绑定
            </text>
            <view class="item-value-with-action">
              <text class="item-value">
                已绑定
              </text>
              <u-icon
                name="checkmark-circle-fill"
                size="16"
                color="#4caf50"
              />
            </view>
          </view>
          
          <view class="info-item">
            <text class="item-label">
              最后登录
            </text>
            <text class="item-value">
              {{ formatDateTime(userInfo.lastLoginAt) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <text class="section-title">
          学习统计
        </text>
        
        <view class="stats-grid">
          <view class="stats-item">
            <text class="stats-number">
              {{ userStats.totalPractice || 0 }}
            </text>
            <text class="stats-label">
              练习次数
            </text>
          </view>
          
          <view class="stats-item">
            <text class="stats-number">
              {{ userStats.totalExams || 0 }}
            </text>
            <text class="stats-label">
              考试次数
            </text>
          </view>
          
          <view class="stats-item">
            <text class="stats-number">
              {{ userStats.passedExams || 0 }}
            </text>
            <text class="stats-label">
              通过考试
            </text>
          </view>
          
          <view class="stats-item">
            <text class="stats-number">
              {{ userStats.certificates || 0 }}
            </text>
            <text class="stats-label">
              获得证书
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        v-if="canEdit"
        class="edit-btn"
        @tap="editProfile"
      >
        编辑资料
      </button>
      
      <button
        class="refresh-btn"
        @tap="refreshInfo"
      >
        刷新信息
      </button>
    </view>

    <!-- 头像预览 -->
    <u-modal
      v-model="showAvatarModal"
      title="头像预览"
      show-cancel-button
      confirm-text="更换头像"
      @confirm="changeAvatar"
      @cancel="showAvatarModal = false"
    >
      <view class="avatar-preview">
        <image 
          class="preview-image"
          :src="userInfo.avatar || '/static/default-avatar.png'"
          mode="aspectFit"
        />
      </view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '../../../../src/stores/modules/user';
import { getUserInfo, getUserStats } from '../../../../src/api/modules/user';
import { storeToRefs } from 'pinia';

const userStore = useUserStore();
const { profile } = storeToRefs(userStore);

// 响应式数据
const userInfo = ref<any>({});
const userStats = ref<any>({});
const loading = ref(false);
const showAvatarModal = ref(false);

// 计算属性
const canEdit = computed(() => {
  return userInfo.value.status === 'new' || userInfo.value.status === 'rejected';
});

onMounted(() => {
  loadUserInfo();
});

/**
 * 加载用户信息
 */
async function loadUserInfo() {
  try {
    loading.value = true;
    
    // 并行加载用户信息和统计数据
    const [userInfoData, userStatsData] = await Promise.all([
      getUserInfo(),
      getUserStats().catch(() => ({})), // 统计数据加载失败不影响主要功能
    ]);
    
    userInfo.value = userInfoData;
    userStats.value = userStatsData;
    
  } catch (error) {
    console.error('加载用户信息失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    new: '未完善资料',
    pending_review: '审核中',
    approved: '已认证',
    rejected: '审核未通过',
  };
  return statusMap[status] || status;
}

/**
 * 格式化手机号
 */
function formatPhone(phone: string) {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 格式化身份证号
 */
function formatIdCard(idCard: string) {
  if (!idCard) return '';
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr: string) {
  if (!dateTimeStr) return '未知';
  return new Date(dateTimeStr).toLocaleString();
}

/**
 * 预览头像
 */
function previewAvatar() {
  if (userInfo.value.avatar) {
    showAvatarModal.value = true;
  }
}

/**
 * 更换头像
 */
function changeAvatar() {
  showAvatarModal.value = false;
  
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里实现头像上传逻辑
      uni.showToast({
        title: '功能开发中',
        icon: 'none',
      });
    },
  });
}

/**
 * 编辑资料
 */
function editProfile() {
  uni.navigateTo({
    url: '/pages/register/register',
  });
}

/**
 * 刷新信息
 */
function refreshInfo() {
  loadUserInfo();
  
  // 同时刷新store中的用户信息
  userStore.refreshProfile();
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.personal-info-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $primary-color, $primary-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-text {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.info-content {
  padding: $spacing-lg;

  .profile-section {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;
    box-shadow: $shadow-medium;

    .avatar-section {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-lg;

      .avatar-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        margin-right: $spacing-lg;
        border: 4rpx solid $divider-color;
      }

      .avatar-info {
        flex: 1;

        .user-nickname {
          display: block;
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-sm;
        }

        .user-status {
          display: inline-block;
          padding: 4rpx 12rpx;
          border-radius: $border-radius-small;
          font-size: $font-size-xs;
          color: white;

          &.approved {
            background-color: $success-color;
          }

          &.pending_review {
            background-color: $warning-color;
          }

          &.rejected {
            background-color: $error-color;
          }

          &.new {
            background-color: $text-disabled;
          }
        }
      }
    }

    .rejection-notice {
      background-color: $error-light;
      border-radius: $border-radius-medium;
      padding: $spacing-md;

      .notice-header {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin-bottom: $spacing-sm;

        .notice-title {
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          color: $error-color;
        }
      }

      .notice-content {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.5;
      }
    }
  }

  .details-section, .account-section, .stats-section {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;
    box-shadow: $shadow-light;

    .section-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-lg;
      padding-bottom: $spacing-sm;
      border-bottom: 2rpx solid $divider-color;
    }

    .info-items {
      .info-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-md 0;
        border-bottom: 1rpx solid $divider-color;

        &:last-child {
          border-bottom: none;
        }

        .item-label {
          font-size: $font-size-md;
          color: $text-secondary;
          margin-right: $spacing-md;
        }

        .item-value {
          flex: 1;
          text-align: right;
          font-size: $font-size-md;
          color: $text-primary;
        }

        .item-value-with-action {
          display: flex;
          align-items: center;
          gap: $spacing-xs;

          .item-value {
            text-align: left;
          }
        }
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-lg;

      .stats-item {
        text-align: center;
        padding: $spacing-lg;
        background-color: $background-color;
        border-radius: $border-radius-medium;

        .stats-number {
          display: block;
          font-size: $font-size-xxl;
          font-weight: $font-weight-bold;
          color: $primary-color;
          margin-bottom: $spacing-xs;
        }

        .stats-label {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }
  }
}

.action-section {
  padding: 0 $spacing-lg $spacing-lg;
  display: flex;
  gap: $spacing-md;

  .edit-btn, .refresh-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
  }

  .edit-btn {
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    color: white;

    &:active {
      opacity: 0.8;
    }
  }

  .refresh-btn {
    background-color: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;

    &:active {
      background-color: $primary-light;
    }
  }
}

.avatar-preview {
  text-align: center;
  padding: $spacing-lg;

  .preview-image {
    width: 400rpx;
    height: 400rpx;
    border-radius: $border-radius-medium;
  }
}
</style>

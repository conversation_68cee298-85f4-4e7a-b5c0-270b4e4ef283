<template>
  <view class="capture-button-area">
    <view
      v-if="faceVerifying"
      class="verifying-state"
    >
      <u-loading-icon mode="circle" color="#66BB6A" size="48" />
      <u-text 
        text="正在验证身份..." 
        size="md" 
        color="#666" 
        align="center" 
        class="status-text"
      />
    </view>
    
    <view v-else-if="cameraAuthorized" class="capture-wrapper">
      <view class="capture-button" @click="handleCaptureFace">
        <view class="button-inner">
          <u-icon name="camera-fill" size="32" color="#ffffff" />
        </view>
      </view>
      <u-text 
        text="点击拍照验证" 
        size="sm" 
        color="#666" 
        align="center" 
        class="button-tip"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  faceVerifying: boolean;
  cameraAuthorized: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'capture-face'): void;
}>();

function handleCaptureFace() {
  emit('capture-face');
}
</script>

<style lang="scss" scoped>
.capture-button-area {
  position: relative;
  z-index: 10;
  padding: 40rpx 0 60rpx 0;
  margin-top: auto;
  flex-shrink: 0;
  
  .verifying-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    padding: 32rpx;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    .status-text {
      display: block;
    }
  }
  
  .capture-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    
    .capture-button {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 32rpx rgba(102, 187, 106, 0.4);
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        width: 140rpx;
        height: 140rpx;
        border: 4rpx solid rgba(102, 187, 106, 0.3);
        border-radius: 70rpx;
        animation: pulse-ring 2s ease-out infinite;
      }
      
      &:active {
        transform: scale(0.95);
      }
      
      .button-inner {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 响应式设计 */
@media screen and (max-height: 600px) {
  .capture-button-area {
    padding-bottom: 24rpx;
    
    .capture-wrapper {
      .capture-button {
        width: 100rpx;
        height: 100rpx;
        
        &::before {
          width: 120rpx;
          height: 120rpx;
        }
      }
    }
  }
  
  .verifying-state {
    padding: 24rpx;
  }
}
</style>
<template>
  <view class="examination-stage">
    <!-- 考试头部 -->
    <view class="exam-header">
      <view class="exam-progress">
        <text class="progress-text">
          {{ currentQuestionIndex + 1 }}/{{ questions.length }}
        </text>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: progressPercent + '%' }"
          />
        </view>
      </view>
      
      <view class="exam-timer">
        <u-icon
          name="clock"
          size="16"
          color="#ff9800"
        />
        <text class="timer-text">
          {{ formatTime(remainingTime) }}
        </text>
      </view>
    </view>

    <!-- 题目内容 -->
    <view class="question-section">
      <view
        v-if="currentQuestion"
        class="question-content"
      >
        <view class="question-header">
          <text class="question-type">
            {{ getQuestionTypeText(currentQuestion.type) }}
          </text>
          <text class="question-score">
            {{ currentQuestion.score }}分
          </text>
        </view>
        
        <text class="question-stem">
          {{ currentQuestion.stem }}
        </text>
        
        <!-- 题目图片 -->
        <image 
          v-if="currentQuestion.image" 
          class="question-image"
          :src="currentQuestion.image"
          mode="widthFix"
          @tap="previewImage(currentQuestion.image)"
        />
        
        <!-- 选项列表 -->
        <view class="options-list">
          <view 
            v-for="(option, index) in currentQuestion.options" 
            :key="index"
            class="option-item"
            :class="{ selected: selectedAnswers.includes(option.key) }"
            @tap="handleSelectOption(option.key)"
          >
            <view class="option-key">
              {{ option.key }}
            </view>
            <text class="option-text">
              {{ option.text }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 答题操作 -->
    <view class="answer-actions">
      <button 
        v-if="currentQuestionIndex > 0"
        class="prev-btn"
        @tap="handlePrevQuestion"
      >
        上一题
      </button>
      
      <button 
        v-if="currentQuestionIndex < questions.length - 1"
        class="next-btn"
        :disabled="selectedAnswers.length === 0"
        @tap="handleNextQuestion"
      >
        下一题
      </button>
      
      <button 
        v-else
        class="submit-btn"
        @tap="handleShowSubmitConfirm"
      >
        提交试卷
      </button>
    </view>

    <!-- 提交确认弹窗 -->
    <u-modal
      v-model="showSubmitModal"
      title="确认提交"
      :content="submitModalContent"
      show-cancel-button
      @confirm="handleSubmitExam"
      @cancel="showSubmitModal = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Props {
  questions: any[];
  currentQuestionIndex: number;
  selectedAnswers: string[];
  allAnswers: Record<number, string[]>;
  remainingTime: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-option', optionKey: string): void;
  (e: 'prev-question'): void;
  (e: 'next-question'): void;
  (e: 'submit-exam'): void;
}>();

const showSubmitModal = ref(false);

const currentQuestion = computed(() => props.questions[props.currentQuestionIndex]);
const progressPercent = computed(() => 
  props.questions.length > 0 ? ((props.currentQuestionIndex + 1) / props.questions.length) * 100 : 0,
);

const submitModalContent = computed(() => {
  const unansweredCount = props.questions.length - Object.keys(props.allAnswers).length;
  if (unansweredCount > 0) {
    return `还有${unansweredCount}题未作答，确定要提交吗？`;
  }
  return '确定要提交试卷吗？提交后不可修改。';
});

function handleSelectOption(optionKey: string) {
  emit('select-option', optionKey);
}

function handlePrevQuestion() {
  emit('prev-question');
}

function handleNextQuestion() {
  emit('next-question');
}

function handleShowSubmitConfirm() {
  showSubmitModal.value = true;
}

function handleSubmitExam() {
  showSubmitModal.value = false;
  emit('submit-exam');
}

function previewImage(imageUrl: string) {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
}

function getQuestionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题',
  };
  return typeMap[type] || type;
}

function formatTime(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.examination-stage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .exam-header {
    background-color: $surface-color;
    padding: $spacing-md $spacing-lg;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid $divider-color;

    .exam-progress {
      flex: 1;

      .progress-text {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }

      .progress-bar {
        height: 6rpx;
        background-color: $divider-color;
        border-radius: 3rpx;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary-color, $primary-light);
          transition: width 0.3s ease;
        }
      }
    }

    .exam-timer {
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .timer-text {
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $warning-color;
      }
    }
  }

  .question-section {
    flex: 1;
    padding: $spacing-lg;

    .question-content {
      .question-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: $spacing-lg;

        .question-type {
          background-color: $primary-color;
          color: white;
          font-size: $font-size-xs;
          padding: 4rpx 12rpx;
          border-radius: $border-radius-small;
        }

        .question-score {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .question-stem {
        display: block;
        font-size: $font-size-lg;
        line-height: 1.6;
        color: $text-primary;
        margin-bottom: $spacing-lg;
        background-color: $surface-color;
        padding: $spacing-xl;
        border-radius: $border-radius-large;
      }

      .question-image {
        width: 100%;
        border-radius: $border-radius-medium;
        margin: $spacing-md 0;
      }

      .options-list {
        .option-item {
          display: flex;
          align-items: flex-start;
          background-color: $surface-color;
          border: 2rpx solid transparent;
          border-radius: $border-radius-medium;
          padding: $spacing-lg;
          margin-bottom: $spacing-md;
          transition: all 0.2s ease;

          &.selected {
            border-color: $primary-color;
            background-color: $primary-light;
          }

          .option-key {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            background-color: $background-color;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: $font-size-md;
            font-weight: $font-weight-medium;
            color: $text-primary;
            margin-right: $spacing-md;
            flex-shrink: 0;
          }

          .option-text {
            flex: 1;
            font-size: $font-size-md;
            line-height: 1.5;
            color: $text-primary;
          }
        }
      }
    }
  }

  .answer-actions {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;
    background-color: $surface-color;

    .prev-btn, .next-btn, .submit-btn {
      flex: 1;
      height: 88rpx;
      border: none;
      border-radius: $border-radius-large;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
    }

    .prev-btn {
      background-color: $text-disabled;
      color: white;
    }

    .next-btn {
      background: linear-gradient(135deg, $primary-color, $primary-dark);
      color: white;

      &:disabled {
        background: $text-disabled;
        opacity: 0.6;
      }
    }

    .submit-btn {
      background: linear-gradient(135deg, $success-color, $success-dark);
      color: white;
    }
  }
}
</style>
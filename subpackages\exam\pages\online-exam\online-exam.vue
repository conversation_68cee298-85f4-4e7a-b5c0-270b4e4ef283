<template>
  <view class="online-exam-container">
    <!-- 考前准备阶段 -->
    <ExamPreparation
      v-if="examStage === 'preparation'"
      :exam-info="examInfo"
      :exam-rules="examRules"
      :countdown-time="countdownTime"
      @start-exam="handleStartExam"
    />

    <!-- 人脸识别阶段 -->
    <FaceVerification
      v-else-if="examStage === 'faceVerification'"
      @camera-ready="onCameraReady"
      @camera-error="onCameraError"
      @face-verified="handleFaceVerified"
      @exit-exam="handleExitExam"
    />

    <!-- 考试进行阶段 -->
    <ExamExecution
      v-else-if="examStage === 'examination'"
      :questions="examQuestions"
      :current-question-index="currentQuestionIndex"
      :selected-answers="selectedAnswers"
      :all-answers="allAnswers"
      :remaining-time="remainingTime"
      @select-option="handleSelectOption"
      @prev-question="handlePrevQuestion"
      @next-question="handleNextQuestion"
      @submit-exam="handleSubmitExam"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { getExamDetail, getExamQuestions, submitExamAnswers, getOnlineExamRules } from '../../../../src/api/modules/exam';
import ExamPreparation from '../../../../src/components/business/ExamPreparation.vue';
import FaceVerification from '../../../../src/components/business/FaceVerification.vue';
import ExamExecution from '../../../../src/components/business/ExamExecution.vue';

const props = defineProps<{
  id: string;
}>();

const examStage = ref<'preparation' | 'faceVerification' | 'examination' | 'completed'>('preparation');
const examInfo = ref<any>({});
const examRules = ref<string>('');
const examQuestions = ref<any[]>([]);
const currentQuestionIndex = ref(0);
const selectedAnswers = ref<string[]>([]);
const allAnswers = ref<Record<number, string[]>>({});
const remainingTime = ref(0);
const countdownTime = ref(5);

let examTimer: NodeJS.Timeout | null = null;
let countdownTimer: NodeJS.Timeout | null = null;

onMounted(() => {
  loadExamInfo();
  loadExamRules();
});

onUnmounted(() => {
  if (examTimer) {
    clearInterval(examTimer);
  }
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

async function loadExamInfo() {
  try {
    examInfo.value = await getExamDetail(props.id);
    remainingTime.value = examInfo.value.duration * 60;
  } catch (error) {
    console.error('加载考试信息失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  }
}

async function loadExamRules() {
  try {
    const response = await getOnlineExamRules(props.id);
    examRules.value = response.rules;
    // 设置倒计时时间，默认5秒，可从response.countdownTime获取
    countdownTime.value = response.countdownTime || 5;
    startCountdown();
  } catch (error) {
    console.error('加载考前须知失败:', error);
    uni.showToast({
      title: '加载考前须知失败',
      icon: 'none',
    });
  }
}

function startCountdown() {
  countdownTimer = setInterval(() => {
    if (countdownTime.value > 0) {
      countdownTime.value--;
    } else {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    }
  }, 1000);
}

function handleStartExam() {
  // 先隐藏当前组件
  examStage.value = '';
  
  // 延迟后再显示人脸识别组件，确保之前的组件完全销毁
  setTimeout(() => {
    examStage.value = 'faceVerification';
  }, 100);
}

function requestCameraPermission() {
  uni.authorize({
    scope: 'scope.camera',
    success: () => {
      console.log('摄像头权限获取成功');
    },
    fail: () => {
      uni.showModal({
        title: '需要摄像头权限',
        content: '考试需要使用摄像头进行身份验证，请在设置中开启权限',
        showCancel: false,
        success: () => {
          uni.navigateBack();
        },
      });
    },
  });
}

function onCameraReady() {
  console.log('摄像头准备就绪');
}

function onCameraError(error: any) {
  console.error('摄像头错误:', error);
  uni.showToast({
    title: '摄像头启动失败',
    icon: 'none',
  });
}

function handleFaceVerified() {
  startExamination();
}

function handleExitExam() {
  uni.navigateBack();
}

async function startExamination() {
  try {
    // 先隐藏人脸识别组件
    examStage.value = '';
    
    // 加载考试题目
    examQuestions.value = await getExamQuestions(props.id);
    
    // 延迟后再显示考试组件
    setTimeout(() => {
      examStage.value = 'examination';
      startExamTimer();
    }, 100);
    
    uni.onAppHide(() => {
      handleAppHide();
    });
  } catch (error) {
    console.error('加载考试题目失败:', error);
    uni.showToast({
      title: '加载题目失败',
      icon: 'none',
    });
  }
}

function startExamTimer() {
  examTimer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      handleSubmitExam();
    }
  }, 1000);
}

function handleAppHide() {
  uni.showModal({
    title: '考试异常',
    content: '检测到您切换了应用，这可能影响考试成绩。请保持在考试界面。',
    showCancel: false,
  });
}

function handleSelectOption(optionKey: string) {
  const question = examQuestions.value[currentQuestionIndex.value];

  if (question.type === 'single') {
    selectedAnswers.value = [optionKey];
  } else if (question.type === 'multiple') {
    const index = selectedAnswers.value.indexOf(optionKey);
    if (index > -1) {
      selectedAnswers.value.splice(index, 1);
    } else {
      selectedAnswers.value.push(optionKey);
    }
  }
}

function handlePrevQuestion() {
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  currentQuestionIndex.value--;
  loadQuestionAnswers();
}

function handleNextQuestion() {
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  currentQuestionIndex.value++;
  loadQuestionAnswers();
}

function loadQuestionAnswers() {
  const savedAnswers = allAnswers.value[currentQuestionIndex.value];
  selectedAnswers.value = savedAnswers ? [...savedAnswers] : [];
}

async function handleSubmitExam() {
  try {
    if (selectedAnswers.value.length > 0) {
      allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
    }

    if (examTimer) {
      clearInterval(examTimer);
    }

    uni.showLoading({ title: '提交中...' });

    const result = await submitExamAnswers({
      examId: props.id,
      answers: allAnswers.value,
      usedTime: (examInfo.value.duration * 60) - remainingTime.value,
    });

    uni.hideLoading();

    uni.redirectTo({
      url: `/subpackages/exam/pages/exam-result/exam-result?examId=${props.id}&score=${result.score}&passed=${result.passed}`,
    });
  } catch (error) {
    uni.hideLoading();
    console.error('提交考试失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    });
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.online-exam-container {
  min-height: 100vh;
  background-color: $background-color;
}
</style>
<template>
  <view class="about-container">
    <!-- 应用信息头部 -->
    <view class="app-header">
      <image 
        class="app-logo"
        src="/static/logo.png"
        mode="aspectFit"
      />
      
      <text class="app-name">
        疾控考试系统
      </text>
      <text class="app-version">
        版本 {{ appVersion }}
      </text>
      <text class="app-desc">
        专业的疾病预防控制培训考试平台
      </text>
    </view>

    <!-- 应用介绍 -->
    <view class="intro-section">
      <text class="section-title">
        应用介绍
      </text>
      
      <view class="intro-content">
        <text class="intro-text">
          疾控考试系统是专为疾病预防控制机构工作人员设计的专业培训考试平台。
          系统提供丰富的学习资源、在线练习、模拟考试等功能，帮助用户提升专业技能，
          顺利通过各类资格认证考试。
        </text>
        
        <view class="features-list">
          <view class="feature-item">
            <view class="feature-icon">
              📚
            </view>
            <text class="feature-text">
              丰富的题库资源，覆盖疾控各个专业领域
            </text>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">
              💻
            </view>
            <text class="feature-text">
              支持线上线下多种考试模式
            </text>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">
              📊
            </view>
            <text class="feature-text">
              智能分析学习进度和薄弱环节
            </text>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">
              🏆
            </view>
            <text class="feature-text">
              权威证书认证，提升职业竞争力
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-section">
      <text class="section-title">
        版本信息
      </text>
      
      <view class="version-info">
        <view class="info-item">
          <text class="info-label">
            当前版本
          </text>
          <text class="info-value">
            {{ appVersion }}
          </text>
        </view>
        
        <view class="info-item">
          <text class="info-label">
            发布时间
          </text>
          <text class="info-value">
            {{ releaseDate }}
          </text>
        </view>
        
        <view class="info-item">
          <text class="info-label">
            应用大小
          </text>
          <text class="info-value">
            {{ appSize }}
          </text>
        </view>
        
        <view class="info-item">
          <text class="info-label">
            支持平台
          </text>
          <text class="info-value">
            微信小程序
          </text>
        </view>
      </view>
      
      <button
        class="check-update-btn"
        @tap="checkUpdate"
      >
        检查更新
      </button>
    </view>

    <!-- 联系我们 -->
    <view class="contact-section">
      <text class="section-title">
        联系我们
      </text>
      
      <view class="contact-info">
        <view
          class="contact-item"
          @tap="callPhone"
        >
          <view class="contact-icon">
            📞
          </view>
          <view class="contact-content">
            <text class="contact-label">
              客服电话
            </text>
            <text class="contact-value">
              ************
            </text>
          </view>
        </view>
        
        <view
          class="contact-item"
          @tap="copyEmail"
        >
          <view class="contact-icon">
            📧
          </view>
          <view class="contact-content">
            <text class="contact-label">
              邮箱地址
            </text>
            <text class="contact-value">
              <EMAIL>
            </text>
          </view>
        </view>
        
        <view
          class="contact-item"
          @tap="copyWechat"
        >
          <view class="contact-icon">
            💬
          </view>
          <view class="contact-content">
            <text class="contact-label">
              微信客服
            </text>
            <text class="contact-value">
              CDC_Service
            </text>
          </view>
        </view>
        
        <view class="contact-item">
          <view class="contact-icon">
            🏢
          </view>
          <view class="contact-content">
            <text class="contact-label">
              公司地址
            </text>
            <text class="contact-value">
              北京市朝阳区疾控大厦8层
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 法律信息 -->
    <view class="legal-section">
      <text class="section-title">
        法律信息
      </text>
      
      <view class="legal-links">
        <view
          class="legal-item"
          @tap="viewPrivacyPolicy"
        >
          <text class="legal-text">
            隐私政策
          </text>
          <u-icon
            name="arrow-right"
            size="14"
            color="#999"
          />
        </view>
        
        <view
          class="legal-item"
          @tap="viewUserAgreement"
        >
          <text class="legal-text">
            用户协议
          </text>
          <u-icon
            name="arrow-right"
            size="14"
            color="#999"
          />
        </view>
        
        <view
          class="legal-item"
          @tap="viewCopyright"
        >
          <text class="legal-text">
            版权声明
          </text>
          <u-icon
            name="arrow-right"
            size="14"
            color="#999"
          />
        </view>
      </view>
    </view>

    <!-- 技术支持 -->
    <view class="tech-section">
      <text class="section-title">
        技术支持
      </text>
      
      <view class="tech-info">
        <view class="tech-item">
          <text class="tech-label">
            开发团队
          </text>
          <text class="tech-value">
            疾控信息技术部
          </text>
        </view>
        
        <view class="tech-item">
          <text class="tech-label">
            技术架构
          </text>
          <text class="tech-value">
            uni-app + Vue3 + TypeScript
          </text>
        </view>
        
        <view class="tech-item">
          <text class="tech-label">
            数据安全
          </text>
          <text class="tech-value">
            SSL加密传输，符合国家信息安全标准
          </text>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright-section">
      <text class="copyright-text">
        © 2024 疾病预防控制中心 版权所有
      </text>
      <text class="copyright-text">
        京ICP备12345678号-1
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 响应式数据
const appVersion = ref('1.0.0');
const releaseDate = ref('2024-06-21');
const appSize = ref('15.2MB');

onMounted(() => {
  getCurrentTime();
});

/**
 * 获取当前时间作为发布时间
 */
function getCurrentTime() {
  const now = new Date();
  releaseDate.value = now.toLocaleDateString();
}

/**
 * 检查更新
 */
function checkUpdate() {
  uni.showModal({
    title: '检查更新',
    content: '当前已是最新版本',
    showCancel: false,
  });
}

/**
 * 拨打电话
 */
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: '************',
  });
}

/**
 * 复制邮箱
 */
function copyEmail() {
  uni.setClipboardData({
    data: '<EMAIL>',
    success: () => {
      uni.showToast({
        title: '邮箱地址已复制',
        icon: 'success',
      });
    },
  });
}

/**
 * 复制微信号
 */
function copyWechat() {
  uni.setClipboardData({
    data: 'CDC_Service',
    success: () => {
      uni.showToast({
        title: '微信号已复制',
        icon: 'success',
      });
    },
  });
}

/**
 * 查看隐私政策
 */
function viewPrivacyPolicy() {
  uni.navigateTo({
    url: '/pages/webview/webview?url=https://cdcexam.com/privacy&title=隐私政策',
  });
}

/**
 * 查看用户协议
 */
function viewUserAgreement() {
  uni.navigateTo({
    url: '/pages/webview/webview?url=https://cdcexam.com/agreement&title=用户协议',
  });
}

/**
 * 查看版权声明
 */
function viewCopyright() {
  uni.navigateTo({
    url: '/pages/webview/webview?url=https://cdcexam.com/copyright&title=版权声明',
  });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.about-container {
  min-height: 100vh;
  background-color: $background-color;
}

.app-header {
  background: linear-gradient(135deg, $primary-color, $primary-light);
  padding: $spacing-xxl $spacing-lg;
  text-align: center;
  color: white;

  .app-logo {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    margin-bottom: $spacing-lg;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
  }

  .app-name {
    display: block;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-sm;
  }

  .app-version {
    display: block;
    font-size: $font-size-md;
    opacity: 0.8;
    margin-bottom: $spacing-md;
  }

  .app-desc {
    font-size: $font-size-sm;
    opacity: 0.7;
    line-height: 1.5;
  }
}

.intro-section, .version-section, .contact-section, .legal-section, .tech-section {
  margin: $spacing-lg;
  background-color: $surface-color;
  border-radius: $border-radius-large;
  padding: $spacing-xl;
  box-shadow: $shadow-light;

  .section-title {
    display: block;
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-sm;
    border-bottom: 2rpx solid $divider-color;
  }
}

.intro-section {
  .intro-content {
    .intro-text {
      display: block;
      font-size: $font-size-md;
      color: $text-secondary;
      line-height: 1.6;
      margin-bottom: $spacing-lg;
    }

    .features-list {
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: $spacing-md;

        .feature-icon {
          font-size: $font-size-lg;
          margin-right: $spacing-md;
          margin-top: 2rpx;
        }

        .feature-text {
          flex: 1;
          font-size: $font-size-sm;
          color: $text-secondary;
          line-height: 1.5;
        }
      }
    }
  }
}

.version-section {
  .version-info {
    margin-bottom: $spacing-lg;

    .info-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-md 0;
      border-bottom: 1rpx solid $divider-color;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: $font-size-md;
        color: $text-secondary;
      }

      .info-value {
        font-size: $font-size-md;
        color: $text-primary;
        font-weight: $font-weight-medium;
      }
    }
  }

  .check-update-btn {
    width: 100%;
    height: 72rpx;
    background-color: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;
    border-radius: $border-radius-medium;
    font-size: $font-size-sm;

    &:active {
      background-color: $primary-light;
    }
  }
}

.contact-section {
  .contact-info {
    .contact-item {
      display: flex;
      align-items: center;
      padding: $spacing-md 0;
      border-bottom: 1rpx solid $divider-color;

      &:last-child {
        border-bottom: none;
      }

      .contact-icon {
        font-size: $font-size-lg;
        margin-right: $spacing-md;
      }

      .contact-content {
        flex: 1;

        .contact-label {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-xs;
        }

        .contact-value {
          font-size: $font-size-md;
          color: $text-primary;
        }
      }
    }
  }
}

.legal-section {
  .legal-links {
    .legal-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-md 0;
      border-bottom: 1rpx solid $divider-color;

      &:last-child {
        border-bottom: none;
      }

      .legal-text {
        font-size: $font-size-md;
        color: $text-primary;
      }
    }
  }
}

.tech-section {
  .tech-info {
    .tech-item {
      margin-bottom: $spacing-md;

      &:last-child {
        margin-bottom: 0;
      }

      .tech-label {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }

      .tech-value {
        font-size: $font-size-md;
        color: $text-primary;
        line-height: 1.5;
      }
    }
  }
}

.copyright-section {
  text-align: center;
  padding: $spacing-xl $spacing-lg;

  .copyright-text {
    display: block;
    font-size: $font-size-xs;
    color: $text-disabled;
    margin-bottom: $spacing-xs;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

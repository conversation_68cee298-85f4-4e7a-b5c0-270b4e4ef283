<template>
  <view class="offline-exam-container">
    <!-- 加载状态 -->
    <view
      v-if="loading"
      class="loading-state"
    >
      <u-loading-icon mode="spinner" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <!-- 考试详情 -->
    <view
      v-else-if="examInfo"
      class="exam-content"
    >
      <!-- 考试详情展示 -->
      <ExamDetailsDisplay :exam-info="examInfo" />

      <!-- 报名状态和操作 -->
      <RegistrationManager
        :exam-info="examInfo"
        :registration-info="registrationInfo"
        @register="handleRegister"
        @cancel-registration="handleCancelRegistration"
        @download-admission-ticket="handleDownloadAdmissionTicket"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getExamDetail, registerExam, cancelExamRegistration, getRegistrationInfo } from '../../../../src/api/modules/exam';
import ExamDetailsDisplay from '../../../../src/components/business/ExamDetailsDisplay.vue';
import RegistrationManager from '../../../../src/components/business/RegistrationManager.vue';

const props = defineProps<{
  id: string;
}>();

const examInfo = ref<any>(null);
const registrationInfo = ref<any>(null);
const loading = ref(false);

onMounted(() => {
  loadExamInfo();
});

async function loadExamInfo() {
  try {
    loading.value = true;

    // 并行加载考试信息和报名信息
    const [examData, registrationData] = await Promise.all([
      getExamDetail(props.id),
      getRegistrationInfo(props.id).catch(() => null), // 如果未报名会返回错误，这里忽略
    ]);

    examInfo.value = examData;
    registrationInfo.value = registrationData;

  } catch (error) {
    console.error('加载考试信息失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

async function handleRegister() {
  try {
    uni.showLoading({ title: '报名中...' });

    const result = await registerExam(props.id);

    uni.hideLoading();

    // 更新报名信息
    registrationInfo.value = result;

    // 更新考试信息中的报名人数
    if (examInfo.value) {
      examInfo.value.registeredCount++;
    }

    uni.showToast({
      title: '报名成功',
      icon: 'success',
    });

  } catch (error) {
    uni.hideLoading();
    console.error('报名失败:', error);
    uni.showToast({
      title: '报名失败，请重试',
      icon: 'none',
    });
  }
}

async function handleCancelRegistration() {
  try {
    uni.showLoading({ title: '取消中...' });

    await cancelExamRegistration(props.id);

    uni.hideLoading();

    // 清除报名信息
    registrationInfo.value = null;

    // 更新考试信息中的报名人数
    if (examInfo.value) {
      examInfo.value.registeredCount--;
    }

    uni.showToast({
      title: '取消成功',
      icon: 'success',
    });

  } catch (error) {
    uni.hideLoading();
    console.error('取消报名失败:', error);
    uni.showToast({
      title: '取消失败，请重试',
      icon: 'none',
    });
  }
}

function handleDownloadAdmissionTicket() {
  if (!registrationInfo.value) return;

  // 这里实现准考证下载逻辑
  uni.showModal({
    title: '下载准考证',
    content: '准考证将保存到相册，请在考试当天携带打印版本',
    showCancel: false,
    success: () => {
      // 实际项目中这里会调用下载API
      uni.showToast({
        title: '准考证已保存',
        icon: 'success',
      });
    },
  });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.offline-exam-container {
  min-height: 100vh;
  background-color: $background-color;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-text {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.exam-content {
  // 子组件自带样式，这里无需额外样式
}
</style>